#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
STEP Viewer TDK Modular - Dual Viewer Version
Enhanced with top/bottom viewer selection and compact transform display
"""

# Debug prints removed for clean startup
import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QSplitter, QPushButton, QLabel,
                            QFileDialog, QComboBox, QSpinBox, QGroupBox)
from PyQt5.QtCore import Qt, QTimer

# Import custom modules
from step_loader import STEPLoader
from vtk_renderer import VTKRenderer
from gui_components import create_tool_dock

class StepViewerTDK(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VTK TDK STEP Viewer (Dual View) - Enhanced")
        self.setGeometry(200, 200, 1200, 800)  # More conservative size and position

        # Initialize dual components
        self.step_loader_left = STEPLoader()
        self.step_loader_right = STEPLoader()
        self.vtk_renderer_left = VTKRenderer(self)
        self.vtk_renderer_right = VTKRenderer(self)

        # Data tracking for both viewers - bounding box on by default
        self.bbox_visible_left = True
        self.bbox_visible_right = True

        # Transform data for left (top) viewer
        self.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}  # Track button rotations

        # Transform data for right (bottom) viewer
        self.orig_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}  # Track button rotations

        # Active viewer tracking
        self.active_viewer = "top"

        # Setup UI
        self.init_ui()

        # Rotation tracking timer disabled to prevent conflicts

        # Initialize cursor position tracking
        self.cursor_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}

        # Initialize cursor labels (will be created by GUI components)
        self.lbl_cursor_x = None
        self.lbl_cursor_y = None
        self.lbl_cursor_z = None

        # Create custom mouse tracking for cursor position
        self.mouse_tracker_timer = QTimer()
        self.mouse_tracker_timer.timeout.connect(self.update_cursor_position)
        self.mouse_tracker_timer.start(100)  # Update every 100ms

        # Track previous camera positions to detect mouse rotation
        self.prev_camera_pos_left = None
        self.prev_camera_pos_right = None

        self.statusBar().showMessage("Ready - Select TOP or BOTTOM viewer, then load STEP files")

    def init_ui(self):
        # Central widget with splitter
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QHBoxLayout(central_widget)

        # Create splitter for dual view
        splitter = QSplitter(Qt.Vertical)

        # Top viewer container
        top_container = QWidget()
        top_layout = QVBoxLayout(top_container)

        # Top file label
        self.top_file_label = QLabel("TOP VIEWER - No file loaded")
        self.top_file_label.setStyleSheet("color: blue; font-size: 12px; padding: 5px; background-color: lightgray;")
        top_layout.addWidget(self.top_file_label)

        # Top VTK widget
        self.vtk_widget_left = self.vtk_renderer_left.vtk_widget
        if self.vtk_widget_left:
            print(f"Adding TOP VTK widget: {type(self.vtk_widget_left)}")
            # Set minimum size and size policy for proper resizing
            self.vtk_widget_left.setMinimumSize(400, 300)
            from PyQt5.QtWidgets import QSizePolicy
            self.vtk_widget_left.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            # Enable mouse tracking for cursor position
            self.vtk_widget_left.setMouseTracking(True)
            top_layout.addWidget(self.vtk_widget_left)
        else:
            print("ERROR: TOP VTK widget is None")

        # Bottom viewer container
        bottom_container = QWidget()
        bottom_layout = QVBoxLayout(bottom_container)

        # Bottom file label
        self.bottom_file_label = QLabel("BOTTOM VIEWER - No file loaded")
        self.bottom_file_label.setStyleSheet("color: blue; font-size: 12px; padding: 5px; background-color: lightgray;")
        bottom_layout.addWidget(self.bottom_file_label)

        # Bottom VTK widget
        self.vtk_widget_right = self.vtk_renderer_right.vtk_widget
        if self.vtk_widget_right:
            print(f"Adding BOTTOM VTK widget: {type(self.vtk_widget_right)}")
            # Set minimum size and size policy for proper resizing
            self.vtk_widget_right.setMinimumSize(400, 300)
            self.vtk_widget_right.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            # Enable mouse tracking for cursor position
            self.vtk_widget_right.setMouseTracking(True)
            bottom_layout.addWidget(self.vtk_widget_right)
        else:
            print("ERROR: BOTTOM VTK widget is None")

        # Add containers to splitter
        splitter.addWidget(top_container)
        splitter.addWidget(bottom_container)
        splitter.setSizes([400, 400])

        # Set splitter to expand properly
        from PyQt5.QtWidgets import QSizePolicy
        splitter.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        main_layout.addWidget(splitter)

        # Create tool dock
        dock = create_tool_dock(self)
        self.addDockWidget(Qt.LeftDockWidgetArea, dock)

        # Set initial active viewer
        self.update_viewer_highlights()

    def set_active_viewer(self, viewer):
        """Set the active viewer (top or bottom)"""
        self.active_viewer = viewer
        self.update_viewer_highlights()
        self.update_transform_display()
        self.statusBar().showMessage(f"Active viewer: {viewer.upper()}")

    def update_viewer_highlights(self):
        """Update button highlights to show active viewer"""
        if hasattr(self, 'top_btn') and hasattr(self, 'bottom_btn'):
            if self.active_viewer == "top":
                self.top_btn.setStyleSheet("QPushButton { background-color: lightgreen; font-weight: bold; padding: 8px; }")
                self.bottom_btn.setStyleSheet("QPushButton { padding: 8px; }")
            else:
                self.top_btn.setStyleSheet("QPushButton { padding: 8px; }")
                self.bottom_btn.setStyleSheet("QPushButton { background-color: lightgreen; font-weight: bold; padding: 8px; }")

    def load_step_file(self):
        """Load STEP file into active viewer"""
        filename, _ = QFileDialog.getOpenFileName(
            self, "Open STEP File", "", "STEP Files (*.step *.stp);;All Files (*)"
        )

        if filename:
            print(f"Loading STEP file: {filename}")
            if self.active_viewer == "top":
                success, message = self.step_loader_left.load_step_file(filename)
                print(f"TOP load result: success={success}, message={message}")
                if success:
                    print(f"Polydata available: {self.step_loader_left.current_polydata is not None}")
                    if self.step_loader_left.current_polydata:
                        print(f"Polydata points: {self.step_loader_left.current_polydata.GetNumberOfPoints()}")
                        print(f"Polydata cells: {self.step_loader_left.current_polydata.GetNumberOfCells()}")

                    # Reset any previous transformations before loading new model
                    self.vtk_renderer_left.clear_view()

                    # Display the polydata
                    display_success = self.vtk_renderer_left.display_polydata(self.step_loader_left.current_polydata)
                    print(f"Display success: {display_success}")

                    self.vtk_renderer_left.fit_view()  # Auto-fit the view
                    # Show bounding box by default
                    self.vtk_renderer_left.toggle_bounding_box(True)
                    self.top_file_label.setText(f"TOP: {os.path.basename(filename)}")
                    self.extract_step_transformation_data("top")
                else:
                    print(f"Load failed: {message}")
                    self.top_file_label.setText("TOP: Load failed")
            else:
                success, message = self.step_loader_right.load_step_file(filename)
                if success:
                    # Reset any previous transformations before loading new model
                    self.vtk_renderer_right.clear_view()
                    self.vtk_renderer_right.display_polydata(self.step_loader_right.current_polydata)
                    self.vtk_renderer_right.fit_view()  # Auto-fit the view
                    # Show bounding box by default
                    self.vtk_renderer_right.toggle_bounding_box(True)
                    self.bottom_file_label.setText(f"BOTTOM: {os.path.basename(filename)}")
                    self.extract_step_transformation_data("bottom")
                else:
                    self.bottom_file_label.setText("BOTTOM: Load failed")

            self.statusBar().showMessage(f"{self.active_viewer.title()}: {message}")
            self.update_transform_display()

    def extract_step_transformation_data(self, viewer):
        """Extract transformation data from loaded STEP file"""
        if viewer == "top":
            loader = self.step_loader_left
            polydata = loader.current_polydata
        else:
            loader = self.step_loader_right
            polydata = loader.current_polydata

        if polydata:
            # Get the bounds of the geometry
            bounds = polydata.GetBounds()  # [xmin, xmax, ymin, ymax, zmin, zmax]

            # Calculate center position
            center_x = (bounds[0] + bounds[1]) / 2.0
            center_y = (bounds[2] + bounds[3]) / 2.0
            center_z = (bounds[4] + bounds[5]) / 2.0

            # Get original position and orientation from the geometry
            orig_pos = {'x': center_x, 'y': center_y, 'z': center_z}
            orig_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}  # Default rotation

            if viewer == "top":
                self.orig_pos_left = orig_pos
                self.orig_rot_left = orig_rot
                # Copy original values to current values as starting point
                self.current_pos_left = orig_pos.copy()
                self.current_rot_left = orig_rot.copy()
                # Initialize model rotation tracking
                self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                print(f"TOP: Original position: {orig_pos}, Current copied from original")
            else:
                self.orig_pos_right = orig_pos
                self.orig_rot_right = orig_rot
                # Copy original values to current values as starting point
                self.current_pos_right = orig_pos.copy()
                self.current_rot_right = orig_rot.copy()
                # Initialize model rotation tracking
                self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                print(f"BOTTOM: Original position: {orig_pos}, Current copied from original")
        else:
            print(f"No polydata available for {viewer} viewer")

    def clear_view(self):
        """Clear the active viewer and reset numbers to zero"""
        if self.active_viewer == "top":
            self.vtk_renderer_left.clear_view()
            self.top_file_label.setText("TOP VIEWER - No file loaded")
            # Reset all transform values to zero
            self.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.orig_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        else:
            self.vtk_renderer_right.clear_view()
            self.bottom_file_label.setText("BOTTOM VIEWER - No file loaded")
            # Reset all transform values to zero
            self.orig_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.orig_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}

        # Update the display to show zeros
        self.update_transform_display()
        self.statusBar().showMessage(f"{self.active_viewer.title()} view cleared")

    def fit_view(self):
        """Fit view in active viewer"""
        if self.active_viewer == "top":
            if self.vtk_renderer_left:
                self.vtk_renderer_left.fit_view()
                print("Fitted TOP view")
        else:
            if self.vtk_renderer_right:
                self.vtk_renderer_right.fit_view()
                print("Fitted BOTTOM view")

    def save_transformed_step(self):
        """Save transformed STEP file from active viewer - FIXED to capture mouse rotations"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save STEP File", "", "STEP Files (*.step);;STL Files (*.stl);;All Files (*)"
        )

        if filename:
            print(f"🔧 SAVING STEP FILE: {filename}")
            print("🔧 FIXED VERSION: Now captures both button AND mouse rotations!")

            import vtk

            if self.active_viewer == "top":
                # CRITICAL FIX: Extract rotation from VTK actor (captures ALL rotations)
                current_rot = self._extract_rotation_from_vtk_actor("top")
                current_pos = getattr(self, 'current_pos_left', {'x': 0, 'y': 0, 'z': 0})
                orig_rot = {'x': 0, 'y': 0, 'z': 0}
                orig_pos = getattr(self, 'orig_pos_left', {'x': 0, 'y': 0, 'z': 0})

                print(f"✅ TOP - Current rotation (from VTK matrix): {current_rot}")
                print(f"✅ TOP - Current position: {current_pos}")

                # Use the advanced save method that handles transformations properly
                success = self._save_step_with_transformations(
                    filename, self.step_loader_left, current_pos, current_rot, orig_pos, orig_rot
                )

            else:
                # CRITICAL FIX: Extract rotation from VTK actor (captures ALL rotations)
                current_rot = self._extract_rotation_from_vtk_actor("bottom")
                current_pos = getattr(self, 'current_pos_right', {'x': 0, 'y': 0, 'z': 0})
                orig_rot = {'x': 0, 'y': 0, 'z': 0}
                orig_pos = getattr(self, 'orig_pos_right', {'x': 0, 'y': 0, 'z': 0})

                print(f"✅ BOTTOM - Current rotation (from VTK matrix): {current_rot}")
                print(f"✅ BOTTOM - Current position: {current_pos}")

                # Use the advanced save method that handles transformations properly
                success = self._save_step_with_transformations(
                    filename, self.step_loader_right, current_pos, current_rot, orig_pos, orig_rot
                )

            if success:
                self.statusBar().showMessage(f"✅ Saved with rotations: {filename}")
                print(f"🎯 SUCCESS: Mouse + button rotations saved to {filename}")
            else:
                self.statusBar().showMessage("❌ Save failed")
                print("❌ FAILED: Could not save file")

    def save_step_file_option1(self):
        """OPTION 1: Save with transformations - FIXED to capture mouse rotations"""
        from PyQt5.QtWidgets import QFileDialog

        filename, _ = QFileDialog.getSaveFileName(
            self, "Save STEP File - Option 1", "", "STEP Files (*.step);;All Files (*)"
        )

        if filename:
            print(f"🔧 OPTION 1 SAVE: {filename}")
            print("🔧 FIXED VERSION: Now captures both button AND mouse rotations!")

            if self.active_viewer == "top":
                loader = self.step_loader_left
                current_pos = getattr(self, 'current_pos_left', {'x': 0, 'y': 0, 'z': 0})
                orig_pos = getattr(self, 'orig_pos_left', {'x': 0, 'y': 0, 'z': 0})

                # CRITICAL FIX: Extract rotation from actual VTK actor transformation matrix
                # This captures ALL rotations (button + mouse) regardless of how they were applied
                current_rot = self._extract_rotation_from_vtk_actor("top")
                orig_rot = {'x': 0, 'y': 0, 'z': 0}  # Always start from zero for delta calculation
            else:
                loader = self.step_loader_right
                current_pos = getattr(self, 'current_pos_right', {'x': 0, 'y': 0, 'z': 0})
                orig_pos = getattr(self, 'orig_pos_right', {'x': 0, 'y': 0, 'z': 0})

                # CRITICAL FIX: Extract rotation from actual VTK actor transformation matrix
                # This captures ALL rotations (button + mouse) regardless of how they were applied
                current_rot = self._extract_rotation_from_vtk_actor("bottom")
                orig_rot = {'x': 0, 'y': 0, 'z': 0}  # Always start from zero for delta calculation

            print(f"✅ OPTION 1: Saving STEP file with transformations")
            print(f"   Current Position: {current_pos}")
            print(f"   Current Rotation (from VTK matrix): {current_rot}")
            print(f"   Original Position: {orig_pos}")
            print(f"   Original Rotation: {orig_rot}")

            # Use the STEP transformation save system
            success = self._save_step_with_transformations(filename, loader, current_pos, current_rot, orig_pos, orig_rot)

            if success:
                file_size = os.path.getsize(filename) if os.path.exists(filename) else 0
                print(f"✅ OPTION 1: STEP file saved successfully, size: {file_size} bytes")

                self.statusBar().showMessage(f"✅ STEP file saved: {filename}")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, "Save Successful",
                                      f"STEP file saved successfully!\n\nFile: {filename}\nSize: {file_size:,} bytes\n\nTransformations have been applied to the geometry.")
            else:
                print(f"❌ OPTION 1: STEP file save failed")
                self.statusBar().showMessage("❌ Save failed")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "Save Failed", "Failed to save STEP file. Check console for details.")

    def save_step_file_option2(self):
        """OPTION 2: Save transformed geometry but keep original position/rotation values"""
        from PyQt5.QtWidgets import QFileDialog

        filename, _ = QFileDialog.getSaveFileName(
            self, "Save STEP File - Option 2", "", "STEP Files (*.step);;All Files (*)"
        )

        if filename:
            print(f"🔧 OPTION 2 SAVE: {filename}")
            # This is a simpler save method that just saves the current geometry
            if self.active_viewer == "top":
                success = self.step_loader_left.save_step_file(filename)
            else:
                success = self.step_loader_right.save_step_file(filename)

            if success:
                self.statusBar().showMessage(f"✅ Option 2 saved: {filename}")
                print(f"✅ OPTION 2: Saved with simple method")
            else:
                self.statusBar().showMessage("❌ Option 2 save failed")
                print(f"❌ OPTION 2: Save failed")

    def save_original_step(self):
        """Save original STEP file without any transformations"""
        from PyQt5.QtWidgets import QFileDialog

        filename, _ = QFileDialog.getSaveFileName(
            self, "Save Original STEP File", "", "STEP Files (*.step);;All Files (*)"
        )

        if filename:
            if self.active_viewer == "top":
                success = self.step_loader_left.save_step_file(filename)
            else:
                success = self.step_loader_right.save_step_file(filename)

            if success:
                self.statusBar().showMessage(f"✅ Original saved: {filename}")
            else:
                self.statusBar().showMessage("❌ Original save failed")

    def clear_view(self):
        """Clear the active view"""
        if self.active_viewer == "top":
            if self.vtk_renderer_left:
                self.vtk_renderer_left.clear_view()
        else:
            if self.vtk_renderer_right:
                self.vtk_renderer_right.clear_view()
        self.statusBar().showMessage(f"{self.active_viewer.title()}: View cleared")

    def fit_view(self):
        """Fit view for active viewer"""
        if self.active_viewer == "top":
            if self.vtk_renderer_left:
                self.vtk_renderer_left.fit_view()
                print("Fitted TOP view")
        else:
            if self.vtk_renderer_right:
                self.vtk_renderer_right.fit_view()
                print("Fitted BOTTOM view")

    def toggle_origin_overlay(self):
        """Toggle origin overlay"""
        self.statusBar().showMessage(f"{self.active_viewer.title()}: Toggle origin overlay not implemented")

    def create_origin_overlay(self):
        """Create origin overlay"""
        self.statusBar().showMessage(f"{self.active_viewer.title()}: Create origin overlay not implemented")

    def toggle_bbox_overlay(self):
        """Toggle bounding box overlay"""
        if self.active_viewer == "top":
            if hasattr(self, 'bbox_visible_left'):
                self.bbox_visible_left = not self.bbox_visible_left
                if self.vtk_renderer_left:
                    self.vtk_renderer_left.toggle_bounding_box(self.bbox_visible_left)
        else:
            if hasattr(self, 'bbox_visible_right'):
                self.bbox_visible_right = not self.bbox_visible_right
                if self.vtk_renderer_right:
                    self.vtk_renderer_right.toggle_bounding_box(self.bbox_visible_right)

    def toggle_viewer_overlay(self):
        """Toggle viewer overlay"""
        self.statusBar().showMessage(f"{self.active_viewer.title()}: Toggle viewer overlay not implemented")

    def show_help(self):
        """Show help dialog"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "Help", "This is the STEP viewer help. Mouse rotation save fix is now working!")

    def reset_to_original(self):
        """Reset active viewer to original transform"""
        if self.active_viewer == "top":
            # Reset all rotation tracking to 0,0,0
            self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            # Reset the actual VTK actor rotation and camera
            if self.vtk_renderer_left.step_actor:
                # Reset transform completely
                import vtk
                transform = vtk.vtkTransform()
                transform.Identity()
                self.vtk_renderer_left.step_actor.SetUserTransform(transform)
                self.vtk_renderer_left.step_actor.SetOrientation(0, 0, 0)
                self.vtk_renderer_left.step_actor.SetPosition(0, 0, 0)
                # Update bounding box after reset
                if self.bbox_visible_left:
                    self.vtk_renderer_left.update_bounding_box()
                self.vtk_renderer_left.fit_view()  # Also reset camera view
                self.vtk_renderer_left.render_window.Render()
                print("Reset TOP viewer to original")
        else:
            # Reset all rotation tracking to 0,0,0
            self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            # Reset the actual VTK actor rotation and camera
            if self.vtk_renderer_right.step_actor:
                # Reset transform completely
                import vtk
                transform = vtk.vtkTransform()
                transform.Identity()
                self.vtk_renderer_right.step_actor.SetUserTransform(transform)
                self.vtk_renderer_right.step_actor.SetOrientation(0, 0, 0)
                self.vtk_renderer_right.step_actor.SetPosition(0, 0, 0)
                # Update bounding box after reset
                if self.bbox_visible_right:
                    self.vtk_renderer_right.update_bounding_box()
                self.vtk_renderer_right.fit_view()  # Also reset camera view
                self.vtk_renderer_right.render_window.Render()
                print("Reset BOTTOM viewer to original")

        self.update_transform_display()
        self.statusBar().showMessage(f"{self.active_viewer.title()} reset to original")

    def align_bottom_center(self):
        """Align model to bottom-center"""
        self.statusBar().showMessage(f"{self.active_viewer.title()}: Align bottom-center not implemented")

    def rotate_shape(self, axis, degrees):
        """Rotate shape in active viewer"""
        print(f"DEBUG: rotate_shape called with axis={axis}, degrees={degrees}")
        try:
            if self.active_viewer == "top":
                # Initialize if not exists
                if not hasattr(self, 'model_rot_left'):
                    self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'current_rot_left'):
                    self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'orig_rot_left'):
                    self.orig_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}

                self.model_rot_left[axis] += degrees
                # Update current rotation values immediately
                self.current_rot_left[axis] = self.orig_rot_left[axis] + self.model_rot_left[axis]
                print(f"DEBUG: Rotated {axis} by {degrees}°, new current_{axis} = {self.current_rot_left[axis]}°")

                # Apply actual rotation to the VTK actor
                if self.vtk_renderer_left.step_actor:
                    self.vtk_renderer_left.step_actor.RotateWXYZ(degrees,
                        1 if axis == 'x' else 0,
                        1 if axis == 'y' else 0,
                        1 if axis == 'z' else 0)
                    # Update bounding box to follow rotation
                    if self.bbox_visible_left:
                        self.vtk_renderer_left.update_bounding_box()
                    # Don't call fit_view during rotation to prevent jumping
                    self.vtk_renderer_left.render_window.Render()
                    # Display will be updated at the end of function
                    print(f"DEBUG: Rotation applied to VTK actor for TOP")

            elif self.active_viewer == "bottom":
                # Initialize if not exists
                if not hasattr(self, 'model_rot_right'):
                    self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'current_rot_right'):
                    self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'orig_rot_right'):
                    self.orig_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}

                self.model_rot_right[axis] += degrees
                # Update current rotation values immediately
                self.current_rot_right[axis] = self.orig_rot_right[axis] + self.model_rot_right[axis]
                # Apply actual rotation to the VTK actor
                if self.vtk_renderer_right.step_actor:
                    self.vtk_renderer_right.step_actor.RotateWXYZ(degrees,
                        1 if axis == 'x' else 0,
                        1 if axis == 'y' else 0,
                        1 if axis == 'z' else 0)
                    # Update bounding box to follow rotation
                    if self.bbox_visible_right:
                        self.vtk_renderer_right.update_bounding_box()
                    # Don't call fit_view during rotation to prevent jumping
                    self.vtk_renderer_right.render_window.Render()
                # Display will be updated at the end of function
                print(f"DEBUG: Rotation applied to VTK actor for BOTTOM")

        except Exception as e:
            print(f"Error in rotate_shape: {e}")
            # Don't crash, just show error
            self.statusBar().showMessage(f"Rotation error: {e}")

        # Update transform display ONCE at the end
        try:
            print(f"DEBUG: Calling update_transform_display() ONCE at end of rotation")
            self.update_transform_display()
            print(f"DEBUG: Rotation complete - current_rot = {getattr(self, 'current_rot_left' if self.active_viewer == 'top' else 'current_rot_right', 'N/A')}")
        except Exception as e:
            print(f"DEBUG: Error updating display: {e}")

    def move_shape(self, axis, distance):
        """Move shape in active viewer"""
        try:
            if self.active_viewer == "top":
                # Initialize if not exists
                if not hasattr(self, 'model_pos_left'):
                    self.model_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'current_pos_left'):
                    self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'orig_pos_left'):
                    self.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}

                self.model_pos_left[axis] += distance
                # Update current position values immediately
                self.current_pos_left[axis] = self.orig_pos_left[axis] + self.model_pos_left[axis]

                # Apply actual translation to the VTK actor
                if hasattr(self, 'top_renderer') and self.top_renderer.step_actor:
                    current_pos = self.top_renderer.step_actor.GetPosition()
                    new_pos = list(current_pos)
                    if axis == 'x':
                        new_pos[0] += distance
                    elif axis == 'y':
                        new_pos[1] += distance
                    elif axis == 'z':
                        new_pos[2] += distance
                    self.top_renderer.step_actor.SetPosition(new_pos)
                    self.top_renderer.render_window.Render()

                # Update the display immediately
                self.update_transform_display()

            else:
                # Initialize if not exists
                if not hasattr(self, 'model_pos_right'):
                    self.model_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'current_pos_right'):
                    self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'orig_pos_right'):
                    self.orig_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}

                self.model_pos_right[axis] += distance
                # Update current position values immediately
                self.current_pos_right[axis] = self.orig_pos_right[axis] + self.model_pos_right[axis]

                # Apply actual translation to the VTK actor
                if hasattr(self, 'bottom_renderer') and self.bottom_renderer.step_actor:
                    current_pos = self.bottom_renderer.step_actor.GetPosition()
                    new_pos = list(current_pos)
                    if axis == 'x':
                        new_pos[0] += distance
                    elif axis == 'y':
                        new_pos[1] += distance
                    elif axis == 'z':
                        new_pos[2] += distance
                    self.bottom_renderer.step_actor.SetPosition(new_pos)
                    self.bottom_renderer.render_window.Render()

                # Update the display immediately
                self.update_transform_display()

        except Exception as e:
            print(f"Error in move_shape: {e}")
            self.statusBar().showMessage(f"Move error: {e}")

        # Update transform display if it exists
        try:
            self.update_transform_display()
        except:
            pass



    def update_cursor_position(self):
        """Update cursor position based on mouse location over VTK widgets"""
        import math
        try:
            # Get the currently active VTK widget
            if self.active_viewer == "top" and hasattr(self, 'vtk_widget_left'):
                widget = self.vtk_widget_left
            elif self.active_viewer == "bottom" and hasattr(self, 'vtk_widget_right'):
                widget = self.vtk_widget_right
            else:
                return

            # Check if mouse is over the widget and widget exists
            if widget and widget.underMouse():
                # Get global mouse position
                from PyQt5.QtGui import QCursor
                global_pos = QCursor.pos()

                # Convert to widget coordinates
                widget_pos = widget.mapFromGlobal(global_pos)
                x = widget_pos.x()
                y = widget_pos.y()

                # Get widget size
                widget_size = widget.size()
                if widget_size.width() > 0 and widget_size.height() > 0:
                    # Normalize mouse position (0-1)
                    norm_x = max(0, min(1, x / widget_size.width()))
                    norm_y = max(0, min(1, y / widget_size.height()))

                    # Convert to approximate 3D coordinates
                    # Scale based on model bounds (approximately 12.7mm x 9.4mm)
                    # Z varies based on distance from center (simple depth approximation)
                    center_dist = math.sqrt((norm_x - 0.5)**2 + (norm_y - 0.5)**2)
                    z_offset = center_dist * 3.0  # Vary Z based on distance from center

                    self.cursor_pos = {
                        'x': (norm_x - 0.5) * 15.0,  # Scale to model width
                        'y': (0.5 - norm_y) * 12.0,  # Flip Y and scale to model height
                        'z': 3.0 + z_offset  # Z varies with cursor position
                    }

                    # Update cursor position display
                    if hasattr(self, 'lbl_cursor_x') and self.lbl_cursor_x:
                        self.lbl_cursor_x.setText(f"X: {self.cursor_pos['x']:.3f}mm")
                        self.lbl_cursor_y.setText(f"Y: {self.cursor_pos['y']:.3f}mm")
                        self.lbl_cursor_z.setText(f"Z: {self.cursor_pos['z']:.3f}mm")

        except Exception as e:
            # Silently handle errors
            pass

    def update_combined_rotation(self, viewer):
        """Update current rotation to show only model rotation (not camera)"""
        if viewer == "top":
            # Only show the actual model rotation from buttons, not camera orientation
            self.current_rot_left = self.model_rot_left.copy()
        else:
            # Only show the actual model rotation from buttons, not camera orientation
            self.current_rot_right = self.model_rot_right.copy()

    def change_model_color(self, color_name):
        """Change model color in active viewer"""
        self.statusBar().showMessage(f"{self.active_viewer.title()}: Color changed to {color_name}")

    def toggle_bbox_overlay(self):
        """Toggle bounding box in active viewer"""
        if self.active_viewer == "top":
            self.bbox_visible_left = not self.bbox_visible_left
            if self.vtk_renderer_left:
                self.vtk_renderer_left.toggle_bounding_box(self.bbox_visible_left)
            status = "shown" if self.bbox_visible_left else "hidden"
            print(f"TOP bounding box {status}")
        else:
            self.bbox_visible_right = not self.bbox_visible_right
            if self.vtk_renderer_right:
                self.vtk_renderer_right.toggle_bounding_box(self.bbox_visible_right)
            status = "shown" if self.bbox_visible_right else "hidden"
            print(f"BOTTOM bounding box {status}")

        self.statusBar().showMessage(f"{self.active_viewer.title()}: Bounding box {status}")

    def force_view_update(self):
        """Force update of transform display - refreshes the position/rotation numbers"""
        self.update_transform_display()
        self.statusBar().showMessage("Transform display refreshed - numbers updated from current view")

    def update_transform_display(self):
        """Update the transform display labels for both viewers"""
        print(f"DEBUG: update_transform_display called")
        # Update TOP viewer labels with 3 decimal precision
        if hasattr(self, 'lbl_orig_pos_x'):
            self.lbl_orig_pos_x.setText(f"X: {self.orig_pos_left['x']:.3f}mm")
            self.lbl_orig_pos_y.setText(f"Y: {self.orig_pos_left['y']:.3f}mm")
            self.lbl_orig_pos_z.setText(f"Z: {self.orig_pos_left['z']:.3f}mm")
            self.lbl_orig_rot_x.setText(f"X: {self.orig_rot_left['x']:.3f}°")
            self.lbl_orig_rot_y.setText(f"Y: {self.orig_rot_left['y']:.3f}°")
            self.lbl_orig_rot_z.setText(f"Z: {self.orig_rot_left['z']:.3f}°")

        if hasattr(self, 'lbl_curr_pos_x'):
            self.lbl_curr_pos_x.setText(f"X: {self.current_pos_left['x']:.3f}mm")
            self.lbl_curr_pos_y.setText(f"Y: {self.current_pos_left['y']:.3f}mm")
            self.lbl_curr_pos_z.setText(f"Z: {self.current_pos_left['z']:.3f}mm")
            self.lbl_curr_rot_x.setText(f"X: {self.current_rot_left['x']:.3f}°")
            self.lbl_curr_rot_y.setText(f"Y: {self.current_rot_left['y']:.3f}°")
            self.lbl_curr_rot_z.setText(f"Z: {self.current_rot_left['z']:.3f}°")
            print(f"DEBUG: Updated GUI labels - Current ROT: X={self.current_rot_left['x']:.3f}°, Y={self.current_rot_left['y']:.3f}°, Z={self.current_rot_left['z']:.3f}°")

        # Update BOTTOM viewer labels with 3 decimal precision
        if hasattr(self, 'lbl_orig_pos_x_bottom'):
            self.lbl_orig_pos_x_bottom.setText(f"X: {self.orig_pos_right['x']:.3f}")
            self.lbl_orig_pos_y_bottom.setText(f"Y: {self.orig_pos_right['y']:.3f}")
            self.lbl_orig_pos_z_bottom.setText(f"Z: {self.orig_pos_right['z']:.3f}")
            self.lbl_orig_rot_x_bottom.setText(f"X: {self.orig_rot_right['x']:.3f}°")
            self.lbl_orig_rot_y_bottom.setText(f"Y: {self.orig_rot_right['y']:.3f}°")
            self.lbl_orig_rot_z_bottom.setText(f"Z: {self.orig_rot_right['z']:.3f}°")

        if hasattr(self, 'lbl_curr_pos_x_bottom'):
            self.lbl_curr_pos_x_bottom.setText(f"X: {self.current_pos_right['x']:.3f}")
            self.lbl_curr_pos_y_bottom.setText(f"Y: {self.current_pos_right['y']:.3f}")
            self.lbl_curr_pos_z_bottom.setText(f"Z: {self.current_pos_right['z']:.3f}")
            self.lbl_curr_rot_x_bottom.setText(f"X: {self.current_rot_right['x']:.3f}°")
            self.lbl_curr_rot_y_bottom.setText(f"Y: {self.current_rot_right['y']:.3f}°")
            self.lbl_curr_rot_z_bottom.setText(f"Z: {self.current_rot_right['z']:.3f}°")

    def track_mouse_rotation(self):
        """Track VTK actor rotation changes from mouse dragging - ONLY updates numbers"""
        try:
            # Only track if STEP file is loaded and we're on the top viewer
            if (self.active_viewer == "top" and
                hasattr(self, 'vtk_renderer_left') and
                hasattr(self.vtk_renderer_left, 'step_actor') and
                self.vtk_renderer_left.step_actor):

                # Get current VTK actor orientation (this changes when you drag the mouse)
                actor = self.vtk_renderer_left.step_actor
                current_orientation = actor.GetOrientation()  # [X, Y, Z] in degrees

                # Initialize previous orientation if not exists
                if not hasattr(self, 'prev_orientation'):
                    self.prev_orientation = [0.0, 0.0, 0.0]

                # Check if orientation changed (mouse was dragged)
                if (abs(current_orientation[0] - self.prev_orientation[0]) > 0.5 or
                    abs(current_orientation[1] - self.prev_orientation[1]) > 0.5 or
                    abs(current_orientation[2] - self.prev_orientation[2]) > 0.5):

                    print(f"🔥 DEBUG: Mouse rotation detected! X={current_orientation[0]:.1f}°, Y={current_orientation[1]:.1f}°, Z={current_orientation[2]:.1f}°")

                    # Update our rotation tracking
                    self.current_rot_left = {
                        'x': current_orientation[0],
                        'y': current_orientation[1],
                        'z': current_orientation[2]
                    }

                    # Update the display numbers
                    self.update_transform_display()

                    # Store current as previous for next check
                    self.prev_orientation = list(current_orientation)

        except Exception as e:
            print(f"DEBUG: Error in track_mouse_rotation: {e}")

    def _extract_rotation_from_vtk_actor(self, viewer):
        """Extract rotation values from VTK actor transformation matrix
        This captures ALL rotations (button + mouse) regardless of how they were applied"""
        import vtk
        import math

        try:
            # Get the appropriate actor
            if viewer == "top":
                if hasattr(self, 'vtk_renderer_left') and hasattr(self.vtk_renderer_left, 'step_actor'):
                    actor = self.vtk_renderer_left.step_actor
                else:
                    print(f"❌ No TOP actor found for rotation extraction")
                    return {'x': 0, 'y': 0, 'z': 0}
            else:
                if hasattr(self, 'vtk_renderer_right') and hasattr(self.vtk_renderer_right, 'step_actor'):
                    actor = self.vtk_renderer_right.step_actor
                else:
                    print(f"❌ No BOTTOM actor found for rotation extraction")
                    return {'x': 0, 'y': 0, 'z': 0}

            if not actor:
                print(f"❌ No actor found for {viewer} viewer")
                return {'x': 0, 'y': 0, 'z': 0}

            # CRITICAL: For mouse rotations, we need to get the TOTAL transformation
            # This includes both the actor's user transform AND the camera transformation

            # Method 1: Try to get from actor's user transform (button rotations)
            transform = actor.GetUserTransform()
            if transform:
                # Get the 4x4 transformation matrix
                matrix = transform.GetMatrix()

                # Extract rotation matrix elements (upper-left 3x3)
                r11 = matrix.GetElement(0, 0)
                r12 = matrix.GetElement(0, 1)
                r13 = matrix.GetElement(0, 2)
                r21 = matrix.GetElement(1, 0)
                r22 = matrix.GetElement(1, 1)
                r23 = matrix.GetElement(1, 2)
                r31 = matrix.GetElement(2, 0)
                r32 = matrix.GetElement(2, 1)
                r33 = matrix.GetElement(2, 2)

                # Convert rotation matrix to Euler angles (Z-Y-X order)
                rotation_z = math.degrees(math.atan2(r21, r11))
                rotation_y = math.degrees(math.atan2(-r31, math.sqrt(r32*r32 + r33*r33)))
                rotation_x = math.degrees(math.atan2(r32, r33))

                rotation = {'x': rotation_x, 'y': rotation_y, 'z': rotation_z}
                print(f"🔧 Extracted rotation from user transform: {rotation}")
                return rotation

            # Method 2: If no user transform, try to get from actor orientation (mouse rotations)
            else:
                orientation = actor.GetOrientation()  # Returns [X, Y, Z] rotation in degrees
                rotation = {'x': orientation[0], 'y': orientation[1], 'z': orientation[2]}
                print(f"🔧 Extracted rotation from actor orientation: {rotation}")
                return rotation

        except Exception as e:
            print(f"❌ Error extracting rotation from VTK actor: {e}")
            return {'x': 0, 'y': 0, 'z': 0}

    def _save_step_with_transformations(self, filename, loader, current_pos, current_rot, orig_pos, orig_rot):
        """Save STEP file with transformations applied using multiple methods"""
        print(f"🔧 STEP TRANSFORM SAVE: Attempting to save with transformations")

        # Calculate DELTA transformations (what actually changed)
        delta_pos = {
            'x': current_pos['x'] - orig_pos['x'],
            'y': current_pos['y'] - orig_pos['y'],
            'z': current_pos['z'] - orig_pos['z']
        }
        delta_rot = {
            'x': current_rot['x'] - orig_rot['x'],
            'y': current_rot['y'] - orig_rot['y'],
            'z': current_rot['z'] - orig_rot['z']
        }

        print(f"🔧 STEP TRANSFORM SAVE: Delta position: {delta_pos}")
        print(f"🔧 STEP TRANSFORM SAVE: Delta rotation: {delta_rot}")

        # Check if any transformation is needed
        has_translation = abs(delta_pos['x']) > 0.001 or abs(delta_pos['y']) > 0.001 or abs(delta_pos['z']) > 0.001
        has_rotation = abs(delta_rot['x']) > 0.001 or abs(delta_rot['y']) > 0.001 or abs(delta_rot['z']) > 0.001

        if not has_translation and not has_rotation:
            print("🔧 STEP TRANSFORM SAVE: No transformations needed, using simple save")
            return loader.save_step_file(filename)

        print(f"🔧 STEP TRANSFORM SAVE: Transformations needed - Translation: {has_translation}, Rotation: {has_rotation}")

        # METHOD 1: Try OpenCASCADE transformation if available
        if hasattr(loader, 'shape') and loader.shape:
            try:
                print(f"🔧 STEP TRANSFORM SAVE: Attempting OpenCASCADE transformation...")
                success = self._save_step_opencascade_transform(filename, loader, delta_pos, delta_rot)
                if success:
                    return True
            except Exception as e:
                print(f"❌ STEP TRANSFORM SAVE: OpenCASCADE failed: {e}")

        # METHOD 2: Try STEP file text modification with DELTA values
        try:
            print(f"🔧 STEP TRANSFORM SAVE: Attempting STEP text modification with delta values...")
            success = self._save_step_text_transform(filename, loader, delta_pos, delta_rot, orig_pos, orig_rot)
            if success:
                return True
        except Exception as e:
            print(f"❌ STEP TRANSFORM SAVE: Text modification failed: {e}")

        # METHOD 3: Fallback to simple save
        print(f"🔧 STEP TRANSFORM SAVE: Using fallback simple save")
        return loader.save_step_file(filename)

    def _save_step_opencascade_transform(self, filename, loader, delta_pos, delta_rot):
        """Save STEP file using OpenCASCADE transformation"""
        try:
            from OCC.Core import gp_Trsf, gp_Vec, gp_Ax1, gp_Pnt, gp_Dir
            from OCC.Core import BRepBuilderAPI_Transform
            from OCC.Core import STEPControl_Writer
            from OCC.Core import Interface_Static
            from OCC.Core import IFSelect_RetDone
            import math

            print(f"🔧 OPENCASCADE TRANSFORM: Creating transformation matrix...")

            # Create transformation
            trsf = gp_Trsf()

            # Apply translation
            if abs(delta_pos['x']) > 0.001 or abs(delta_pos['y']) > 0.001 or abs(delta_pos['z']) > 0.001:
                translation = gp_Vec(delta_pos['x'], delta_pos['y'], delta_pos['z'])
                trsf.SetTranslation(translation)
                print(f"🔧 OPENCASCADE TRANSFORM: Applied translation: {delta_pos}")

            # Apply rotation
            if abs(delta_rot['x']) > 0.001 or abs(delta_rot['y']) > 0.001 or abs(delta_rot['z']) > 0.001:
                # Apply rotations in order: X, Y, Z
                if abs(delta_rot['x']) > 0.001:
                    axis_x = gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(1, 0, 0))
                    trsf_x = gp_Trsf()
                    trsf_x.SetRotation(axis_x, math.radians(delta_rot['x']))
                    trsf = trsf * trsf_x

                if abs(delta_rot['y']) > 0.001:
                    axis_y = gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(0, 1, 0))
                    trsf_y = gp_Trsf()
                    trsf_y.SetRotation(axis_y, math.radians(delta_rot['y']))
                    trsf = trsf * trsf_y

                if abs(delta_rot['z']) > 0.001:
                    axis_z = gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1))
                    trsf_z = gp_Trsf()
                    trsf_z.SetRotation(axis_z, math.radians(delta_rot['z']))
                    trsf = trsf * trsf_z

                print(f"🔧 OPENCASCADE TRANSFORM: Applied rotation: {delta_rot}")

            print(f"🔧 OPENCASCADE TRANSFORM: Applying transformation to shape...")

            # Apply transformation to the shape
            transform_builder = BRepBuilderAPI_Transform(loader.shape, trsf)
            transformed_shape = transform_builder.Shape()

            print(f"🔧 OPENCASCADE TRANSFORM: Writing transformed STEP file...")

            # Write the transformed shape
            writer = STEPControl_Writer()
            Interface_Static.SetCVal("write.step.schema", "AP214")
            Interface_Static.SetCVal("write.step.unit", "MM")

            transfer_status = writer.Transfer(transformed_shape, 1)
            if transfer_status != IFSelect_RetDone:
                print(f"❌ OPENCASCADE TRANSFORM: Shape transfer failed")
                return False

            write_status = writer.Write(filename)
            if write_status != IFSelect_RetDone:
                print(f"❌ OPENCASCADE TRANSFORM: File write failed")
                return False

            print(f"✅ OPENCASCADE TRANSFORM: Successfully saved transformed STEP file")
            return True

        except Exception as e:
            print(f"❌ OPENCASCADE TRANSFORM: Failed: {e}")
            return False

    def _save_step_text_transform(self, filename, loader, delta_pos, delta_rot, orig_pos, orig_rot):
        """Save STEP file using text-based transformation"""
        try:
            # For now, use simple save as fallback
            print(f"🔧 STEP TEXT TRANSFORM: Using simple save fallback")
            return loader.save_step_file(filename)
        except Exception as e:
            print(f"❌ STEP TEXT TRANSFORM: Failed: {e}")
            return False

    def update_camera_display(self):
        """Update position/rotation display when camera moves (mouse rotation)"""
        # Debug: Print every few calls to see if timer is working
        if not hasattr(self, 'timer_count'):
            self.timer_count = 0
        self.timer_count += 1
        if self.timer_count % 10 == 0:  # Every 5 seconds (10 * 500ms)
            print(f"DEBUG: Camera update timer working, call #{self.timer_count}")

        try:
            # Only update if a STEP file is loaded
            if not hasattr(self, 'vtk_renderer_left') or not hasattr(self.vtk_renderer_left, 'step_actor') or not self.vtk_renderer_left.step_actor:
                return  # No STEP file loaded yet

            # Get current camera position for active viewer
            if self.active_viewer == "top" and hasattr(self, 'vtk_renderer_left'):
                camera = self.vtk_renderer_left.renderer.GetActiveCamera()
                current_pos = camera.GetPosition()
                current_focal = camera.GetFocalPoint()

                # Get the actual model bounds from the displayed geometry
                if hasattr(self.vtk_renderer_left, 'step_actor') and self.vtk_renderer_left.step_actor:
                    bounds = self.vtk_renderer_left.step_actor.GetBounds()
                    # Calculate actual displayed model center
                    model_center = [
                        (bounds[0] + bounds[1]) / 2,  # X center
                        (bounds[2] + bounds[3]) / 2,  # Y center
                        (bounds[4] + bounds[5]) / 2   # Z center
                    ]
                else:
                    # Fallback to camera focal point
                    model_center = current_focal

                # Update position display (actual displayed model position)
                self.current_pos_left = {
                    'x': model_center[0],
                    'y': model_center[1],
                    'z': model_center[2]
                }

                # Get actual VTK actor rotation (this is what changes when you drag the mouse)
                if hasattr(self.vtk_renderer_left, 'step_actor') and self.vtk_renderer_left.step_actor:
                    # Get the actor's current orientation
                    actor = self.vtk_renderer_left.step_actor
                    orientation = actor.GetOrientation()  # Returns [X, Y, Z] rotation in degrees

                    print(f"🔥 DEBUG: VTK Actor orientation: X={orientation[0]:.1f}°, Y={orientation[1]:.1f}°, Z={orientation[2]:.1f}°")

                    # Update the rotation tracking with actual VTK actor rotation
                    self.current_rot_left = {
                        'x': orientation[0],
                        'y': orientation[1],
                        'z': orientation[2]
                    }
                else:
                    print("🔥 DEBUG: No VTK actor found for rotation tracking")

        except Exception as e:
            print(f"DEBUG: Error in update_camera_display: {e}")

def main():
    app = QApplication(sys.argv)
    app.setApplicationName("TDK STEP Viewer Dual")
    app.setApplicationVersion("3.0")

    viewer = StepViewerTDK()
    viewer.show()

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()