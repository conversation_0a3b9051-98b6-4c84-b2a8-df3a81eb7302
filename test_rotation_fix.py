#!/usr/bin/env python3
"""
Test the rotation fix - verify buttons add to current values instead of resetting
"""

import sys
import os
os.environ['QT_QPA_PLATFORM'] = 'offscreen'

from PyQt5.QtWidgets import QApplication
app = QApplication([])

import step_viewer_tdk_modular

print("🔘 TESTING ROTATION FIX")
print("=" * 50)

# Create main window
main_window = step_viewer_tdk_modular.StepViewerTDK()

# Initialize rotation tracking
main_window.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
main_window.active_viewer = 'top'

print(f"Initial model_rot_left: {main_window.model_rot_left}")

# Test 3 button clicks
for click in range(1, 4):
    print(f"\n--- Button click #{click} ---")
    
    # Call the actual rotate_shape method
    main_window.rotate_shape('x', 15.0)
    
    current_x = main_window.model_rot_left.get('x', 0.0)
    expected_x = click * 15.0
    
    print(f"Expected X: {expected_x:.1f}°")
    print(f"Actual X: {current_x:.1f}°")
    
    if abs(current_x - expected_x) < 0.1:
        print(f"✅ Click #{click}: CORRECT")
    else:
        print(f"❌ Click #{click}: WRONG")

final_x = main_window.model_rot_left.get('x', 0.0)
print(f"\nFinal result: X={final_x:.1f}°")

if final_x == 45.0:
    print("🎉 ROTATION FIX WORKING: 0° → 15° → 30° → 45°")
else:
    print(f"❌ ROTATION FIX BROKEN: Expected 45°, got {final_x:.1f}°")

app.quit()
