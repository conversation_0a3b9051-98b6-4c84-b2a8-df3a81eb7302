#!/usr/bin/env python3
"""
Simple test to verify the 3D rotation matrix fix works
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# Import the main program
sys.path.append('.')
from step_viewer_tdk_modular import StepViewerTDK

def test_rotation_fix():
    """Test the 3D rotation matrix fix"""
    print("🔧 TESTING 3D ROTATION MATRIX FIX")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load test file
    print("📂 Loading test.step...")
    if not os.path.exists('test.step'):
        print("❌ test.step not found!")
        return
        
    success = viewer.load_step_file_direct('test.step')
    if not success:
        print("❌ Failed to load test.step")
        return
        
    print("✅ File loaded successfully")
    
    # Set active viewer to top
    viewer.active_viewer = "top"
    
    # Test the new 3D rotation matrix method
    print("\n🔧 Testing 3D rotation matrix method...")
    
    # Apply complex multi-axis rotation
    print("🔄 Applying X=30°, Y=45°, Z=60° rotation...")
    
    # Initialize rotation values if not exists
    if not hasattr(viewer, 'current_rot_left'):
        viewer.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    
    # Set the rotation values directly
    viewer.current_rot_left = {'x': 30.0, 'y': 45.0, 'z': 60.0}
    
    # Apply the 3D transformation matrix
    try:
        viewer._apply_3d_rotation_matrix("left", viewer.current_rot_left)
        print("✅ 3D rotation matrix applied successfully")
        print(f"📊 Applied rotation: {viewer.current_rot_left}")
        
        # Test save functionality
        print("\n💾 Testing save functionality...")
        current_pos = getattr(viewer, 'current_pos_left', {'x': 0, 'y': 0, 'z': 0})
        current_rot = viewer.current_rot_left
        orig_pos = getattr(viewer, 'orig_pos_left', {'x': 0, 'y': 0, 'z': 0})
        orig_rot = getattr(viewer, 'orig_rot_left', {'x': 0, 'y': 0, 'z': 0})
        
        save_filename = "simple_rotation_test.step"
        loader = viewer.step_loader_left
        
        success = viewer._save_step_with_transformations(
            save_filename, loader, current_pos, current_rot, orig_pos, orig_rot
        )
        
        if success and os.path.exists(save_filename):
            print(f"✅ File saved successfully: {save_filename}")
            
            # Test load functionality
            print("\n📂 Testing load functionality...")
            viewer.active_viewer = "bottom"
            
            load_success = viewer.load_step_file_direct(save_filename)
            if load_success:
                print("✅ Saved file loaded successfully")
                
                # Compare rotation values
                if hasattr(viewer, 'current_rot_right'):
                    loaded_rotation = viewer.current_rot_right
                    print(f"📊 Original rotation: {current_rot}")
                    print(f"📊 Loaded rotation:   {loaded_rotation}")
                    
                    # Calculate differences
                    diff_x = abs(current_rot['x'] - loaded_rotation['x'])
                    diff_y = abs(current_rot['y'] - loaded_rotation['y'])
                    diff_z = abs(current_rot['z'] - loaded_rotation['z'])
                    
                    print(f"\n🔍 Rotation differences:")
                    print(f"   X: {diff_x:.2f}°")
                    print(f"   Y: {diff_y:.2f}°")
                    print(f"   Z: {diff_z:.2f}°")
                    
                    if diff_x < 1.0 and diff_y < 1.0 and diff_z < 1.0:
                        print("\n✅ SUCCESS: 3D ROTATION MATRIX FIX WORKING!")
                        print("   Complex multi-axis rotation preserved correctly")
                    else:
                        print("\n❌ ISSUE: Rotation not preserved accurately")
                        print("   The 3D matrix fix needs further adjustment")
                else:
                    print("❌ Could not get loaded rotation values")
            else:
                print("❌ Failed to load saved file")
        else:
            print("❌ Save failed")
            
    except Exception as e:
        print(f"❌ 3D rotation matrix failed: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("🎯 TEST COMPLETE")
    
    # Keep the window open for a few seconds
    import time
    time.sleep(3)
    
    app.quit()

if __name__ == "__main__":
    test_rotation_fix()
